@echo off
chcp 65001 >nul
title 银发-满天神佛 后端服务

echo.
echo ==========================================
echo    银发-满天神佛 后端服务启动器
echo ==========================================
echo.

cd /d "%~dp0"

echo 📁 当前目录: %cd%
echo.

:: 检查是否在backend目录
if not exist "package.json" (
    echo ❌ 错误: 找不到package.json文件
    echo 请确保在backend目录中运行此脚本
    pause
    exit /b 1
)

:: 检查Node.js环境
echo 🔄 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js环境未配置或不可用
    echo 请先安装Node.js并配置环境变量
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ Node.js环境正常
    node --version
)

echo.
echo 📦 检查依赖包...
if not exist "node_modules" (
    echo 📥 安装后端依赖包...
    echo 这可能需要几分钟时间，请耐心等待...
    call npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        echo 请检查网络连接或尝试使用cnpm
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖包已存在
    echo 📥 更新依赖包...
    call npm install --silent
)

:: 检查环境变量文件
echo.
echo 🔧 检查环境配置...
if not exist ".env" (
    if exist "env.example" (
        echo ⚠️  未找到.env文件，正在从env.example创建...
        copy env.example .env >nul
        echo ✅ .env文件已创建，请根据需要修改配置
    ) else (
        echo ⚠️  未找到环境配置文件，将使用默认配置
    )
) else (
    echo ✅ 环境配置文件存在
)

echo.
echo 🔥 启动后端服务...
echo.
echo 📋 服务信息:
echo    服务地址: http://localhost:3001
echo    健康检查: http://localhost:3001/health
echo    API文档: http://localhost:3001/api/v1/
echo    环境: Development
echo.
echo 💡 提示:
echo    - 按 Ctrl+C 停止服务
echo    - 服务启动后会自动重载代码变更
echo    - 日志信息将显示在下方
echo.
echo ==========================================
echo.

:: 启动服务
call npm start

echo.
echo 🛑 服务已停止
pause 