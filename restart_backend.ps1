Write-Host "========================================" -ForegroundColor Green
Write-Host "重启后端服务脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "1. 查找并停止占用3001端口的进程..." -ForegroundColor Yellow
try {
    $connections = Get-NetTCPConnection -LocalPort 3001 -ErrorAction SilentlyContinue
    if ($connections) {
        foreach ($conn in $connections) {
            $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "发现进程: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Cyan
                Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
                Write-Host "已停止进程 $($process.Id)" -ForegroundColor Green
            }
        }
    } else {
        Write-Host "没有找到占用3001端口的进程" -ForegroundColor Gray
    }
} catch {
    Write-Host "检查端口时出错，尝试停止所有Node.js进程..." -ForegroundColor Red
}

Write-Host ""
Write-Host "2. 停止所有Node.js进程..." -ForegroundColor Yellow
try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        $nodeProcesses | Stop-Process -Force -ErrorAction SilentlyContinue
        Write-Host "已停止所有Node.js进程" -ForegroundColor Green
    } else {
        Write-Host "没有找到运行中的Node.js进程" -ForegroundColor Gray
    }
} catch {
    Write-Host "停止Node.js进程时出错" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. 等待端口释放..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

Write-Host ""
Write-Host "4. 启动后端服务..." -ForegroundColor Yellow
if (Test-Path "backend/package.json") {
    Set-Location "backend"
    Write-Host "正在启动后端服务..." -ForegroundColor Green
    node server.js
} else {
    Write-Host "错误：未找到backend/package.json文件" -ForegroundColor Red
    Write-Host "请确保在正确的项目目录中运行此脚本" -ForegroundColor Red
}

Read-Host "按Enter键退出" 