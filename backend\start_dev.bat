@echo off
chcp 65001 >nul
title 银发-满天神佛 后端服务 (开发模式)

echo.
echo ==========================================
echo    银发-满天神佛 后端服务 (开发模式)
echo ==========================================
echo.

cd /d "%~dp0"

:: 快速检查
if not exist "package.json" (
    echo ❌ 错误: 请在backend目录中运行此脚本
    pause
    exit /b 1
)

echo 🔄 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 请先安装Node.js
    pause
    exit /b 1
)

echo 📦 检查依赖包...
if not exist "node_modules" (
    echo 📥 安装依赖包...
    call npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo.
echo 🔥 启动开发服务器...
echo 📋 服务地址: http://localhost:3001
echo 💡 开发模式：自动重载 + 详细日志
echo.

:: 使用nodemon启动开发服务器
if exist "node_modules\.bin\nodemon.exe" (
    call npm run dev
) else (
    echo ⚠️  未找到nodemon，使用标准模式启动...
    call npm start
)

pause 